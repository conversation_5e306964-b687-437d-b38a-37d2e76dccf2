/**
 * 消息渲染組件
 * 用於渲染複雜的 AI 回復消息
 */

import React from 'react';
import { Image, Button, Space } from 'antd-mobile';
import {
  ParsedMessageContent,
  TextMessageContent,
  ImageMessageContent,
  ButtonMessageContent,
  FlexMessageContent,
  QuickReplyItem,
  NewQuickReplyItem
} from '@/types/chat';
import QuickReplyButtons from './QuickReplyButtons';

interface MessageRendererProps {
  parsedContent: ParsedMessageContent;
  isUser?: boolean;
  onQuickReplyClick?: (item: QuickReplyItem) => void;
  onNewQuickReplyClick?: (item: NewQuickReplyItem) => void;
  disabled?: boolean;
}

const MessageRenderer: React.FC<MessageRendererProps> = ({
  parsedContent,
  isUser = false,
  onQuickReplyClick,
  onNewQuickReplyClick,
  disabled = false,
}) => {

  // 渲染單個消息內容
  const renderMessageContent = (content: any, index: number) => {
    switch (content.type) {
      case 'Text':
        return renderTextMessage(content, index);
      case 'Image':
        return renderImageMessage(content, index);
      case 'Template':
        return renderButtonMessage(content, index);
      case 'Flex':
        return renderFlexMessage(content, index);
      default:
        return renderUnknownMessage(content, index);
    }
  };

  // 渲染文本消息
  const renderTextMessage = (content: TextMessageContent, index: number) => {
    return (
      <div key={index} className="message-text mb-2">
        <div className="whitespace-pre-wrap break-words text-sm leading-relaxed">
          {content.text}
        </div>
      </div>
    );
  };

  // 渲染圖片消息
  const renderImageMessage = (content: ImageMessageContent, index: number) => {
    return (
      <div key={index} className="message-image mb-2">
        <Image
          src={content.originalContentUrl}
          alt="消息圖片"
          fit="cover"
          style={{
            maxWidth: '200px',
            maxHeight: '200px',
            borderRadius: '8px',
          }}
          placeholder={
            <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-sm">載入中...</span>
            </div>
          }
          fallback={
            <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-sm">圖片載入失敗</span>
            </div>
          }
        />
      </div>
    );
  };

  // 渲染按鈕消息
  const renderButtonMessage = (content: ButtonMessageContent, index: number) => {
    return (
      <div key={index} className="message-buttons mb-2">
        {/* 顯示文本內容 */}
        {content.template.text && (
          <div className="mb-3 text-sm leading-relaxed">
            {content.template.text}
          </div>
        )}
        
        {/* 渲染按鈕 */}
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {content.template.actions.map((action, actionIndex) => (
            <Button
              key={actionIndex}
              size="small"
              color="primary"
              fill="outline"
              disabled={disabled}
              onClick={() => handleButtonClick(action)}
              style={{
                width: '100%',
                textAlign: 'left',
                justifyContent: 'flex-start',
              }}
            >
              {action.label}
            </Button>
          ))}
        </Space>
      </div>
    );
  };

  // 渲染 Flex 消息
  const renderFlexMessage = (content: FlexMessageContent, index: number) => {
    return (
      <div key={index} className="message-flex mb-2">
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-600 mb-2">
            {content.altText || 'Flex 消息'}
          </div>
          <div className="text-xs text-gray-500">
            複雜內容格式，請在支援的客戶端中查看
          </div>
        </div>
      </div>
    );
  };

  // 渲染未知消息類型
  const renderUnknownMessage = (content: any, index: number) => {
    return (
      <div key={index} className="message-unknown mb-2">
        <div className="bg-yellow-50 border border-yellow-200 p-2 rounded text-sm">
          <div className="text-yellow-800 mb-1">未知消息類型</div>
          <div className="text-yellow-600 text-xs font-mono">
            {JSON.stringify(content, null, 2)}
          </div>
        </div>
      </div>
    );
  };

  // 處理按鈕點擊
  const handleButtonClick = (action: any) => {
    if (disabled) return;

    switch (action.type) {
      case 'message':
        // 發送消息
        if (action.text && onNewQuickReplyClick) {
          onNewQuickReplyClick({
            title: action.label,
            displayText: action.text
          });
        }
        break;
      case 'postback':
        // 處理 postback
        if (action.data && onNewQuickReplyClick) {
          onNewQuickReplyClick({
            title: action.label,
            displayText: action.data
          });
        }
        break;
      case 'uri':
        // 打開連結
        if (action.uri) {
          window.open(action.uri, '_blank');
        }
        break;
      default:
        console.warn('Unknown button action type:', action.type);
    }
  };

  return (
    <div className="message-renderer">
      {/* 渲染所有消息內容 */}
      {parsedContent.contents.map((content, index) => 
        renderMessageContent(content, index)
      )}

      {/* 渲染快速回復按鈕 */}
      {parsedContent.hasQuickReply && (
        <QuickReplyButtons
          quickReplyItems={parsedContent.quickReplyItems}
          newQuickReplyItems={parsedContent.newQuickReplyItems}
          onQuickReplyClick={onQuickReplyClick}
          onNewQuickReplyClick={onNewQuickReplyClick}
          disabled={disabled}
        />
      )}
    </div>
  );
};

export default MessageRenderer;
