/**
 * 聊天消息列表組件
 * 顯示聊天消息列表，支援自動滾動和載入狀態
 */

import React, { useEffect, useRef } from 'react'
import { Empty } from 'antd-mobile'
import { ChatMessage as ChatMessageType, QuickReplyItem, NewQuickReplyItem } from '@/types/chat'
import ChatMessage from './ChatMessage'

interface ChatMessageListProps {
  messages: ChatMessageType[]
  isLoading?: boolean
  loadingMessage?: string
  emptyText?: string
  emptyDescription?: string
  onQuickReplyClick?: (item: QuickReplyItem) => void
  onNewQuickReplyClick?: (item: NewQuickReplyItem) => void
  disabled?: boolean
}

const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  isLoading = false,
  loadingMessage = 'AI 正在思考...',
  emptyText = '開始新的對話',
  emptyDescription = '在下方輸入框中輸入您的問題，AI 助手將為您提供幫助',
  onQuickReplyClick,
  onNewQuickReplyClick,
  disabled = false,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自動滾動到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'end'
    })
  }

  // 當消息更新時自動滾動
  useEffect(() => {
    const timer = setTimeout(scrollToBottom, 100)
    return () => clearTimeout(timer)
  }, [messages, isLoading])

  // 如果沒有消息且不在載入中，顯示空狀態
  if (messages.length === 0 && !isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <Empty
          description={
            <div className="text-center">
              <div className="text-4xl mb-4">🤖</div>
              <div className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {emptyText}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {emptyDescription}
              </div>
            </div>
          }
        />
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"
    >
      {/* 消息列表 */}
      <div className="min-h-full">
        {messages.map((message) => (
          <ChatMessage
            key={message.id}
            message={message}
            onQuickReplyClick={onQuickReplyClick}
            onNewQuickReplyClick={onNewQuickReplyClick}
            disabled={disabled}
          />
        ))}

        {/* 載入狀態 */}
        {isLoading && (
          <ChatMessage
            message={{
              id: 'loading',
              type: 'assistant',
              content: loadingMessage,
              timestamp: new Date().toISOString(),
            }}
            isLoading={true}
          />
        )}

        {/* 滾動錨點 */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}

export default ChatMessageList
