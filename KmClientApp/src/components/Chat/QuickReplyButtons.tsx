/**
 * Quick Reply 按鈕組件
 * 用於渲染快速回復按鈕
 */

import React from 'react';
import { Button, Space } from 'antd-mobile';
import { QuickReplyItem, NewQuickReplyItem } from '@/types/chat';

interface QuickReplyButtonsProps {
  quickReplyItems?: QuickReplyItem[];
  newQuickReplyItems?: NewQuickReplyItem[];
  onQuickReplyClick?: (item: QuickReplyItem) => void;
  onNewQuickReplyClick?: (item: NewQuickReplyItem) => void;
  disabled?: boolean;
}

const QuickReplyButtons: React.FC<QuickReplyButtonsProps> = ({
  quickReplyItems = [],
  newQuickReplyItems = [],
  onQuickReplyClick,
  onNewQuickReplyClick,
  disabled = false,
}) => {
  // 如果沒有任何快速回復項目，不渲染
  if (quickReplyItems.length === 0 && newQuickReplyItems.length === 0) {
    return null;
  }

  // 處理舊格式快速回復點擊
  const handleQuickReplyClick = (item: QuickReplyItem) => {
    if (disabled) return;
    onQuickReplyClick?.(item);
  };

  // 處理新格式快速回復點擊
  const handleNewQuickReplyClick = (item: NewQuickReplyItem) => {
    if (disabled) return;
    onNewQuickReplyClick?.(item);
  };

  return (
    <div className="mt-3">
      <Space wrap size="small">
        {/* 渲染舊格式快速回復按鈕 */}
        {quickReplyItems.map((item, index) => (
          <Button
            key={`quick-reply-${index}`}
            size="small"
            color="primary"
            fill="outline"
            disabled={disabled}
            onClick={() => handleQuickReplyClick(item)}
            style={{
              borderRadius: '16px',
              fontSize: '14px',
              padding: '4px 12px',
              height: 'auto',
              minHeight: '32px',
            }}
          >
            {item.action.label}
          </Button>
        ))}

        {/* 渲染新格式快速回復按鈕 */}
        {newQuickReplyItems.map((item, index) => (
          <Button
            key={`new-quick-reply-${index}`}
            size="small"
            color="primary"
            fill="outline"
            disabled={disabled}
            onClick={() => handleNewQuickReplyClick(item)}
            style={{
              borderRadius: '16px',
              fontSize: '14px',
              padding: '4px 12px',
              height: 'auto',
              minHeight: '32px',
            }}
          >
            {item.displayText}
          </Button>
        ))}
      </Space>
    </div>
  );
};

export default QuickReplyButtons;
