/**
 * 聊天消息組件
 * 顯示單個聊天消息，支援用戶和助手消息
 */

import React from 'react'
import { Avatar } from 'antd-mobile'
import { ChatMessage as ChatMessageType, QuickReplyItem, NewQuickReplyItem } from '@/types/chat'
import MessageRenderer from './MessageRenderer'

interface ChatMessageProps {
  message: ChatMessageType
  isLoading?: boolean
  onQuickReplyClick?: (item: QuickReplyItem) => void
  onNewQuickReplyClick?: (item: NewQuickReplyItem) => void
  disabled?: boolean
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isLoading = false,
  onQuickReplyClick,
  onNewQuickReplyClick,
  disabled = false
}) => {
  const isUser = message.type === 'user'
  const isAssistant = message.type === 'assistant'

  return (
    <div className={`flex gap-3 p-4 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
      {/* 頭像 */}
      <div className="flex-shrink-0">
        <Avatar
          size={40}
          style={{
            backgroundColor: isUser ? '#3b82f6' : '#f3f4f6',
            color: isUser ? '#ffffff' : '#374151',
          }}
        >
          {isUser ? '👤' : '🤖'}
        </Avatar>
      </div>

      {/* 消息內容 */}
      <div className={`flex-1 max-w-[80%] ${isUser ? 'text-right' : 'text-left'}`}>
        {/* 消息氣泡 */}
        <div
          className={`inline-block p-3 rounded-2xl ${
            isUser
              ? 'bg-blue-500 text-white rounded-br-md'
              : 'bg-white text-gray-900 border border-gray-200 rounded-bl-md dark:bg-gray-800 dark:text-gray-100 dark:border-gray-600'
          }`}
        >
          {/* 載入狀態 */}
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
              <span className="text-sm opacity-70">AI 正在思考...</span>
            </div>
          ) : (
            // 根據是否有解析後的內容來決定渲染方式
            (() => {
              console.log('💬 ChatMessage 渲染決策:', {
                messageId: message.id,
                messageType: message.type,
                hasParsedContent: !!message.parsedContent,
                parsedContent: message.parsedContent,
                rawContent: message.content
              });

              return message.parsedContent ? (
                <MessageRenderer
                  parsedContent={message.parsedContent}
                  isUser={isUser}
                  onQuickReplyClick={onQuickReplyClick}
                  onNewQuickReplyClick={onNewQuickReplyClick}
                  disabled={disabled}
                />
              ) : (
                <div className="whitespace-pre-wrap break-words">
                  {message.content}
                </div>
              );
            })()
          )}
        </div>

        {/* 時間戳 */}
        {!isLoading && (
          <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {new Date(message.timestamp).toLocaleTimeString('zh-TW', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </div>
        )}
      </div>
    </div>
  )
}

export default ChatMessage
