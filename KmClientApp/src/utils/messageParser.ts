/**
 * 消息解析器 - 處理 LINE Bot 風格的 JSON 消息格式
 * 基於 KMClient 的 MessageParser 類實現
 */

import {
  ParsedMessageContent,
  MessageContent,
  TextMessageContent,
  ImageMessageContent,
  ButtonMessageContent,
  FlexMessageContent,
  QuickReplyItem,
  NewQuickReplyItem,
  QuickReply,
  NewQuickReply
} from '@/types/chat';

/**
 * 消息解析器 - 處理 LINE Bot 風格的 JSON 消息格式
 */
export class MessageParser {
  /**
   * 解析消息內容
   * @param content 原始消息內容（可能是 JSON 字符串或純文本）
   * @returns 解析後的消息結構
   */
  static parseMessage(content: string): ParsedMessageContent {
    console.log('🔍 MessageParser 開始解析:', { content, contentType: typeof content });

    try {
      // 嘗試解析 JSON 格式
      const parsed = JSON.parse(content);
      console.log('✅ JSON 解析成功:', parsed);

      // 如果是數組格式（LINE Bot 標準格式）
      if (Array.isArray(parsed)) {
        console.log('📋 處理數組格式消息');
        return this.parseMessageArray(parsed);
      }

      // 如果是單個對象，包裝成數組處理
      if (typeof parsed === 'object' && parsed !== null) {
        console.log('📦 處理單個對象，包裝成數組');
        return this.parseMessageArray([parsed]);
      }

      // 如果解析成功但不是預期格式，當作純文本處理
      console.log('⚠️ 非預期格式，當作純文本處理');
      return this.createTextMessage(content);

    } catch (error) {
      // JSON 解析失敗，當作純文本處理
      console.log('❌ JSON 解析失敗，當作純文本處理:', error);
      return this.createTextMessage(content);
    }
  }

  /**
   * 解析消息數組
   * @param messages 消息對象數組
   * @returns 解析後的消息結構
   */
  static parseMessageArray(messages: any[]): ParsedMessageContent {
    const contents: MessageContent[] = [];
    let hasQuickReply = false;
    const quickReplyItems: QuickReplyItem[] = [];
    const newQuickReplyItems: NewQuickReplyItem[] = [];

    for (const msg of messages) {
      try {
        const parsedMsg = this.parseMessageObject(msg);
        if (parsedMsg) {
          contents.push(parsedMsg);

          // 收集快速回復項目
          if (parsedMsg.quickReply && parsedMsg.quickReply.items) {
            // 檢查是否為新格式（包含 title 和 displayText）
            if (this.isNewQuickReplyFormat(parsedMsg.quickReply.items)) {
              newQuickReplyItems.push(...(parsedMsg.quickReply.items as NewQuickReplyItem[]));
            } else {
              quickReplyItems.push(...(parsedMsg.quickReply.items as QuickReplyItem[]));
            }
            hasQuickReply = true;
          }
        }
      } catch (error) {
        console.warn('Failed to parse message object:', msg, error);
        // 解析失敗的消息當作文本處理
        contents.push({
          type: 'Text',
          text: JSON.stringify(msg),
          quickReply: null
        });
      }
    }

    return {
      contents,
      hasQuickReply,
      quickReplyItems,
      newQuickReplyItems
    };
  }

  /**
   * 解析單個消息對象
   * @param msg 消息對象
   * @returns 解析後的消息內容
   */
  private static parseMessageObject(msg: any): MessageContent | null {
    if (!msg || typeof msg !== 'object') {
      return null;
    }

    const { type, quickReply } = msg;

    switch (type) {
      case 'Text':
      case 'text':
        return this.parseTextMessage(msg, quickReply);

      case 'Image':
      case 'image':
        return this.parseImageMessage(msg, quickReply);

      case 'Template':
      case 'template':
        return this.parseButtonMessage(msg, quickReply);

      case 'Flex':
      case 'flex':
        return this.parseFlexMessage(msg, quickReply);

      default:
        // 未知類型，嘗試當作文本處理
        console.warn('Unknown message type:', type);
        return {
          type: 'Text',
          text: msg.text || JSON.stringify(msg),
          quickReply: quickReply || null
        };
    }
  }

  /**
   * 解析文本消息
   */
  private static parseTextMessage(msg: any, quickReply?: any): TextMessageContent {
    return {
      type: 'Text',
      text: msg.text || '',
      quickReply: this.parseQuickReply(quickReply)
    };
  }

  /**
   * 解析圖片消息
   */
  private static parseImageMessage(msg: any, quickReply?: any): ImageMessageContent {
    return {
      type: 'Image',
      originalContentUrl: msg.originalContentUrl || '',
      previewImageUrl: msg.previewImageUrl || msg.originalContentUrl || '',
      quickReply: this.parseQuickReply(quickReply)
    };
  }

  /**
   * 解析按鈕消息
   */
  private static parseButtonMessage(msg: any, quickReply?: any): ButtonMessageContent {
    return {
      type: 'Template',
      altText: msg.altText || '',
      template: {
        type: 'buttons',
        text: msg.template?.text || '',
        actions: msg.template?.actions || []
      },
      quickReply: this.parseQuickReply(quickReply)
    };
  }

  /**
   * 解析 Flex 消息
   */
  private static parseFlexMessage(msg: any, quickReply?: any): FlexMessageContent {
    return {
      type: 'Flex',
      altText: msg.altText || '',
      contents: msg.contents || {},
      quickReply: this.parseQuickReply(quickReply)
    };
  }

  /**
   * 解析快速回復
   */
  private static parseQuickReply(quickReply: any): QuickReply | NewQuickReply | null {
    if (!quickReply || !quickReply.items || !Array.isArray(quickReply.items)) {
      return null;
    }

    // 驗證快速回復格式
    if (!this.validateQuickReply(quickReply)) {
      console.warn('Invalid quick reply format:', quickReply);
      return null;
    }

    return quickReply;
  }

  /**
   * 創建純文本消息
   */
  private static createTextMessage(content: string): ParsedMessageContent {
    return {
      contents: [{
        type: 'Text',
        text: content,
        quickReply: null
      }],
      hasQuickReply: false,
      quickReplyItems: [],
      newQuickReplyItems: []
    };
  }

  /**
   * 驗證快速回復格式
   */
  static validateQuickReply(quickReply: any): boolean {
    if (!quickReply || !Array.isArray(quickReply.items)) {
      return false;
    }

    return quickReply.items.every((item: any) => {
      // 檢查新格式（包含 title 和 displayText）
      if (item.title && item.displayText) {
        return typeof item.title === 'string' && typeof item.displayText === 'string';
      }

      // 檢查舊格式（包含 type 和 action）
      if (item.type && item.action) {
        return item.type === 'action' && 
               item.action.type && 
               item.action.label;
      }

      return false;
    });
  }

  /**
   * 檢查是否為新的快速回復格式
   */
  static isNewQuickReplyFormat(items: any[]): boolean {
    if (!Array.isArray(items) || items.length === 0) {
      return false;
    }

    return items.every(item => 
      item && 
      typeof item.title === 'string' && 
      typeof item.displayText === 'string'
    );
  }

  /**
   * 提取純文本內容
   * @param parsedContent 解析後的消息內容
   * @returns 純文本字符串
   */
  static extractPlainText(parsedContent: ParsedMessageContent): string {
    return parsedContent.contents
      .filter(content => content.type === 'Text')
      .map(content => (content as TextMessageContent).text)
      .join('\n');
  }
}
