/**
 * 聊天頁面
 * 提供完整的聊天功能，包括消息列表、輸入框和會話管理
 */

import React, { useEffect, useState } from 'react'
import { Toast, Dialog } from 'antd-mobile'
import { useAppStore } from '@/hooks/useAppStore'
import { useURLParams } from '@/hooks/useURLParams'
import { useChatService } from '@/hooks/useChatService'
import { QuickReplyItem, NewQuickReplyItem } from '@/types/chat'
import ChatHeader from '@/components/Chat/ChatHeader'
import ChatMessageList from '@/components/Chat/ChatMessageList'
import ChatInput from '@/components/Chat/ChatInput'

const ChatPage: React.FC = () => {
  const { isParamsValid, userInfo } = useAppStore()
  const { initializeParams } = useURLParams()
  const {
    currentSession,
    chatLoading,
    error,
    sendMessage,
    createNewChatSession,
    clearCurrentSession,
  } = useChatService()

  const [isInitialized, setIsInitialized] = useState(false)

  // 初始化頁面
  useEffect(() => {
    const initializePage = async () => {
      try {
        // 初始化 URL 參數
        await initializeParams()

        // 如果沒有當前會話，創建一個新會話
        if (!currentSession && isParamsValid) {
          createNewChatSession()
        }

        setIsInitialized(true)
      } catch (error) {
        console.error('聊天頁面初始化失敗:', error)
        Toast.show('頁面初始化失敗')
      }
    }

    initializePage()
  }, [isParamsValid, currentSession, createNewChatSession, initializeParams])

  // 處理發送消息
  const handleSendMessage = async (message: string) => {
    if (!isParamsValid) {
      Toast.show('請先設置正確的 URL 參數')
      return
    }

    if (!currentSession) {
      Toast.show('請先創建會話')
      return
    }

    try {
      await sendMessage(message)
    } catch (error) {
      console.error('發送消息失敗:', error)
    }
  }

  // 處理新建會話
  const handleNewChat = () => {
    if (!isParamsValid) {
      Toast.show('請先設置正確的 URL 參數')
      return
    }

    createNewChatSession()
    Toast.show('已創建新會話')
  }

  // 處理清空會話
  const handleClearChat = () => {
    if (!currentSession || currentSession.messages.length === 0) {
      return
    }

    Dialog.confirm({
      title: '確認清空',
      content: '確定要清空當前會話的所有消息嗎？此操作無法撤銷。',
      onConfirm: () => {
        clearCurrentSession()
        Toast.show('會話已清空')
      },
    })
  }

  // 處理 Quick Reply 點擊（舊格式）
  const handleQuickReplyClick = async (item: QuickReplyItem) => {
    if (!isParamsValid || !currentSession) {
      Toast.show('請先設置正確的 URL 參數')
      return
    }

    try {
      // 根據 action 類型決定發送的內容
      let messageText = ''
      switch (item.action.type) {
        case 'message':
          messageText = item.action.text || item.action.label
          break
        case 'postback':
          messageText = item.action.data || item.action.label
          break
        default:
          messageText = item.action.label
      }

      await sendMessage(messageText)
    } catch (error) {
      console.error('Quick Reply 發送失敗:', error)
    }
  }

  // 處理 Quick Reply 點擊（新格式）
  const handleNewQuickReplyClick = async (item: NewQuickReplyItem) => {
    if (!isParamsValid || !currentSession) {
      Toast.show('請先設置正確的 URL 參數')
      return
    }

    try {
      await sendMessage(item.displayText)
    } catch (error) {
      console.error('Quick Reply 發送失敗:', error)
    }
  }

  // 如果未初始化，顯示載入狀態
  if (!isInitialized) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="text-4xl mb-4">⏳</div>
          <div className="text-lg text-gray-600 dark:text-gray-400">
            正在初始化聊天功能...
          </div>
        </div>
      </div>
    )
  }

  // 如果參數無效，顯示錯誤提示
  if (!isParamsValid) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center p-8">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            需要設置 URL 參數
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            請在 URL 中添加必要的參數：
          </p>
          <div className="text-sm text-gray-500 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
            ?TenantID=your_tenant&ServiceID=your_service&UserID=your_user
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* 聊天頭部 */}
      <ChatHeader
        currentSession={currentSession}
        onNewChat={handleNewChat}
        onClearChat={handleClearChat}
        disabled={chatLoading}
      />

      {/* 消息列表 */}
      <ChatMessageList
        messages={currentSession?.messages || []}
        isLoading={chatLoading}
        loadingMessage="AI 正在思考中..."
        emptyText="開始新的對話"
        emptyDescription="在下方輸入框中輸入您的問題，AI 助手將為您提供幫助"
        onQuickReplyClick={handleQuickReplyClick}
        onNewQuickReplyClick={handleNewQuickReplyClick}
        disabled={chatLoading}
      />

      {/* 輸入框 */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={chatLoading || !isParamsValid}
        placeholder={
          chatLoading
            ? 'AI 正在回應中...'
            : '輸入您的問題...'
        }
        maxLength={2000}
      />

      {/* 錯誤提示 */}
      {error && (
        <div className="p-2 bg-red-50 border-t border-red-200 dark:bg-red-900/20 dark:border-red-800">
          <div className="text-sm text-red-600 dark:text-red-400 text-center">
            {error}
          </div>
        </div>
      )}
    </div>
  )
}

export default ChatPage
