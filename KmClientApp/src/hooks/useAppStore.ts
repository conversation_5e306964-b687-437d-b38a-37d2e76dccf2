/**
 * 全局應用狀態管理
 * 使用 Zustand 管理應用程式的全局狀態
 */

import { create } from 'zustand';
import { URLParams, UserInfo, AppConfig } from '@/types';
import { Attachment, AttachmentType } from '@/types/attachment';
import { ChatSession, ChatMessage } from '@/types/chat';

// 數據轉換函數（暫時保留，後續會使用）
export const transformBackendDataToAttachments = (assets: any[]): Attachment[] => {
  const attachments: Attachment[] = [];

  assets.forEach(asset => {
    const baseAttachment = {
      id: asset.id || `${asset.type}_${Date.now()}_${Math.random()}`,
      name: asset.name || '未命名',
      remark: asset.remark || '',
      created_at: asset.created_at || new Date().toISOString(),
      user_info: asset.user_info || { tenant_id: '', service_id: '', user_id: '' },
    };

    switch (asset.type) {
      case 'file':
        attachments.push({
          ...baseAttachment,
          type: AttachmentType.FILE,
          file_path: asset.file_path || '',
          file_size: asset.file_size || 0,
          mime_type: asset.mime_type || 'application/octet-stream',
          access_path: asset.access_path,
          access_url: asset.access_url,
        });
        break;
      case 'website':
        attachments.push({
          ...baseAttachment,
          type: AttachmentType.WEBSITE,
          url: asset.url || '',
          status: asset.status,
        });
        break;
      case 'youtube':
        attachments.push({
          ...baseAttachment,
          type: AttachmentType.YOUTUBE,
          youtube_link: asset.youtube_link || '',
        });
        break;
      case 'plain_text':
        attachments.push({
          ...baseAttachment,
          type: AttachmentType.PLAIN_TEXT,
          content: asset.content || '',
        });
        break;
      default:
        console.warn('未知的附件類型:', asset.type);
    }
  });

  return attachments;
};

// 應用狀態介面
interface AppState {
  // URL 參數相關
  urlParams: URLParams;
  isParamsValid: boolean;
  paramErrors: string[];

  // 應用狀態
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;

  // 主題和 UI 狀態
  isDarkMode: boolean;
  sidebarCollapsed: boolean;

  // 附件相關
  attachments: Attachment[];
  attachmentsLoading: boolean;

  // 聊天相關
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  chatLoading: boolean;

  // 用戶信息
  userInfo: UserInfo;

  // 應用配置
  config: AppConfig | null;
  
  // 操作方法
  setURLParams: (params: URLParams) => void;
  setParamValidation: (isValid: boolean, errors: string[]) => void;
  setInitialized: (initialized: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  toggleDarkMode: () => void;
  toggleSidebar: () => void;
  reset: () => void;

  // 附件操作
  setAttachments: (attachments: Attachment[]) => void;
  addAttachment: (attachment: Attachment) => void;
  removeAttachment: (id: string) => void;
  updateAttachment: (id: string, updates: Partial<Attachment>) => void;
  setAttachmentsLoading: (loading: boolean) => void;
  refreshAttachments: () => Promise<void>;

  // 聊天操作
  setCurrentSession: (session: ChatSession | null) => void;
  setSessions: (sessions: ChatSession[]) => void;
  addMessageToCurrentSession: (message: ChatMessage) => void;
  updateMessageInCurrentSession: (messageId: string, updates: Partial<ChatMessage>) => void;
  createNewSession: () => ChatSession;
  setChatLoading: (loading: boolean) => void;

  // 用戶信息操作
  setUserInfo: (userInfo: UserInfo) => void;

  // 配置操作
  setConfig: (config: AppConfig) => void;
}

// 初始狀態
const initialState = {
  urlParams: {},
  isParamsValid: false,
  paramErrors: [],
  isInitialized: false,
  isLoading: false,
  error: null,
  isDarkMode: false, // 默認使用淺色主題
  sidebarCollapsed: false,
  attachments: [],
  attachmentsLoading: false,
  currentSession: null,
  sessions: [],
  chatLoading: false,
  userInfo: {
    tenant_id: '',
    service_id: '',
    user_id: '',
  },
  config: null,
};

// 創建 Zustand store
export const useAppStore = create<AppState>((set, get) => ({
  ...initialState,

  // 設置 URL 參數
  setURLParams: (params: URLParams) => {
    set({ urlParams: params });

    // 同時更新 localStorage
    try {
      localStorage.setItem('kmClient_params', JSON.stringify(params));
      localStorage.setItem('kmClient_params_timestamp', Date.now().toString());
    } catch (error) {
      console.error('Failed to store params:', error);
    }
  },

  // 設置參數驗證結果
  setParamValidation: (isValid: boolean, errors: string[]) => {
    set({
      isParamsValid: isValid,
      paramErrors: errors,
      error: isValid ? null : '參數驗證失敗'
    });
  },

  // 設置初始化狀態
  setInitialized: (initialized: boolean) => {
    set({ isInitialized: initialized });
  },

  // 設置載入狀態
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 設置錯誤狀態
  setError: (error: string | null) => {
    set({ error });
  },

  // 切換深色模式
  toggleDarkMode: () => {
    const newDarkMode = !get().isDarkMode;
    set({ isDarkMode: newDarkMode });

    // 更新 DOM 類名
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // 存儲到 localStorage
    try {
      localStorage.setItem('kmClient_darkMode', newDarkMode.toString());
    } catch (error) {
      console.error('Failed to store dark mode state:', error);
    }
  },

  // 切換側邊欄
  toggleSidebar: () => {
    const newCollapsed = !get().sidebarCollapsed;
    set({ sidebarCollapsed: newCollapsed });

    // 存儲到 localStorage
    try {
      localStorage.setItem('kmClient_sidebarCollapsed', newCollapsed.toString());
    } catch (error) {
      console.error('Failed to store sidebar state:', error);
    }
  },

  // 重置狀態
  reset: () => {
    set(initialState);

    // 清除 localStorage
    try {
      localStorage.removeItem('kmClient_params');
      localStorage.removeItem('kmClient_params_timestamp');
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  },

  // 附件相關操作
  setAttachments: (attachments: Attachment[]) => {
    set({ attachments });
  },

  addAttachment: (attachment: Attachment) => {
    set(state => ({
      attachments: [...state.attachments, attachment]
    }));
  },

  removeAttachment: (id: string) => {
    set(state => ({
      attachments: state.attachments.filter(att => att.id !== id)
    }));
  },

  updateAttachment: (id: string, updates: Partial<Attachment>) => {
    set(state => ({
      attachments: state.attachments.map(att =>
        att.id === id ? { ...att, ...updates } as Attachment : att
      )
    }));
  },

  setAttachmentsLoading: (loading: boolean) => {
    set({ attachmentsLoading: loading });
  },

  refreshAttachments: async () => {
    // 這裡將在後續實現 API 調用
    console.log('refreshAttachments: 待實現');
  },

  // 聊天相關操作
  setCurrentSession: (session: ChatSession | null) => {
    set({ currentSession: session });
  },

  setSessions: (sessions: ChatSession[]) => {
    set({ sessions });
  },

  addMessageToCurrentSession: (message: ChatMessage) => {
    console.log('🔄 addMessageToCurrentSession 開始:', {
      messageId: message.id,
      messageType: message.type,
      contentLength: message.content?.length,
      hasParsedContent: !!message.parsedContent,
      parsedContentDetails: message.parsedContent
    });

    set(state => {
      if (!state.currentSession) {
        console.warn('❌ 沒有當前會話，無法添加消息');
        return state;
      }

      console.log('📋 當前會話狀態:', {
        sessionId: state.currentSession.id,
        messagesCount: state.currentSession.messages.length
      });

      const updatedSession = {
        ...state.currentSession,
        messages: [...state.currentSession.messages, message],
        updated_at: new Date().toISOString(),
      };

      console.log('✅ 會話更新完成:', {
        sessionId: updatedSession.id,
        messagesCount: updatedSession.messages.length,
        lastMessage: updatedSession.messages[updatedSession.messages.length - 1]
      });

      const newState = {
        currentSession: updatedSession,
        sessions: state.sessions.map(session =>
          session.id === updatedSession.id ? updatedSession : session
        ),
      };

      console.log('🎯 addMessageToCurrentSession 完成，新狀態:', {
        currentSessionId: newState.currentSession?.id,
        messagesCount: newState.currentSession?.messages.length
      });

      return newState;
    });
  },

  updateMessageInCurrentSession: (messageId: string, updates: Partial<ChatMessage>) => {
    set(state => {
      if (!state.currentSession) return state;

      const updatedSession = {
        ...state.currentSession,
        messages: state.currentSession.messages.map(msg =>
          msg.id === messageId ? { ...msg, ...updates } : msg
        ),
        updated_at: new Date().toISOString(),
      };

      return {
        currentSession: updatedSession,
        sessions: state.sessions.map(session =>
          session.id === updatedSession.id ? updatedSession : session
        ),
      };
    });
  },

  createNewSession: () => {
    const newSession: ChatSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      messages: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      name: `會話 ${new Date().toLocaleString()}`,
    };

    set(state => ({
      currentSession: newSession,
      sessions: [newSession, ...state.sessions],
    }));

    return newSession;
  },

  setChatLoading: (loading: boolean) => {
    set({ chatLoading: loading });
  },

  // 用戶信息操作
  setUserInfo: (userInfo: UserInfo) => {
    set({ userInfo });
  },

  // 配置操作
  setConfig: (config: AppConfig) => {
    set({ config });
  },
}));

// 初始化應用狀態的 Hook
export const useAppInitialization = () => {
  const store = useAppStore();

  // 從存儲恢復狀態
  const restoreFromStorage = () => {
    try {
      // 恢復深色模式設置
      const savedDarkMode = localStorage.getItem('kmClient_darkMode');
      if (savedDarkMode !== null) {
        const isDarkMode = savedDarkMode === 'true';
        if (isDarkMode !== store.isDarkMode) {
          store.toggleDarkMode(); // 這會同步更新 DOM 和狀態
        }
      }

      // 恢復側邊欄狀態
      const savedSidebarState = localStorage.getItem('kmClient_sidebarCollapsed');
      if (savedSidebarState !== null) {
        const isCollapsed = savedSidebarState === 'true';
        if (isCollapsed !== store.sidebarCollapsed) {
          store.toggleSidebar();
        }
      }

      // 恢復參數（如果存在且未過期）
      const storedParams = localStorage.getItem('kmClient_params');
      const timestamp = localStorage.getItem('kmClient_params_timestamp');

      if (storedParams && timestamp) {
        const now = Date.now();
        const storedTime = parseInt(timestamp, 10);
        const maxAge = 24 * 60 * 60 * 1000; // 24小時

        if (now - storedTime <= maxAge) {
          const params = JSON.parse(storedParams);
          store.setURLParams(params);
        } else {
          // 參數已過期，清除
          localStorage.removeItem('kmClient_params');
          localStorage.removeItem('kmClient_params_timestamp');
        }
      }
    } catch (error) {
      console.error('Failed to restore state from storage:', error);
    }
  };

  return {
    restoreFromStorage,
    ...store,
  };
};
