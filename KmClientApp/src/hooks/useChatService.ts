/**
 * 聊天服務 Hook
 * 提供聊天功能的業務邏輯封裝
 */

import { useState, useCallback } from 'react'
import { Toast } from 'antd-mobile'
import { useAppStore } from './useAppStore'
import { apiService } from '@/services/apiService'
import { storageService } from '@/services/storageService'
import { ChatMessage, ChatSession, OmnichannelChatRequest, ChatResponse } from '@/types/chat'
import { MessageParser } from '@/utils/messageParser'

export const useChatService = () => {
  const {
    currentSession,
    userInfo,
    chatLoading,
    setCurrentSession,
    addMessageToCurrentSession,
    updateMessageInCurrentSession,
    createNewSession,
    setChatLoading,
  } = useAppStore()

  const [error, setError] = useState<string | null>(null)

  // 生成消息 ID
  const generateMessageId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [])

  // 生成會話 ID
  const generateSessionId = useCallback(() => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [])

  // 創建用戶消息
  const createUserMessage = useCallback((content: string): ChatMessage => {
    return {
      id: generateMessageId(),
      type: 'user',
      content,
      timestamp: new Date().toISOString(),
    }
  }, [generateMessageId])

  // 創建助手消息
  const createAssistantMessage = useCallback((content: string): ChatMessage => {
    console.log('🤖 創建助手消息:', { content, contentLength: content?.length });

    // 解析消息內容
    const parsedContent = MessageParser.parseMessage(content)
    console.log('📝 消息解析結果:', {
      parsedContent,
      contentsLength: parsedContent?.contents?.length,
      contents: parsedContent?.contents,
      hasQuickReply: parsedContent?.hasQuickReply,
      quickReplyItems: parsedContent?.quickReplyItems,
      newQuickReplyItems: parsedContent?.newQuickReplyItems
    });

    const message = {
      id: generateMessageId(),
      type: 'assistant' as const,
      content,
      parsedContent,
      timestamp: new Date().toISOString(),
    }

    console.log('✅ 助手消息創建完成:', {
      message,
      messageId: message.id,
      messageType: message.type,
      contentLength: message.content?.length,
      hasParsedContent: !!message.parsedContent,
      parsedContentDetails: message.parsedContent
    });
    return message;
  }, [generateMessageId])

  // 保存會話到本地存儲
  const saveSessionToStorage = useCallback(async (session: ChatSession) => {
    try {
      await storageService.set(`chat_session_${session.id}`, session, {
        type: 'persistent',
        encrypt: false,
      })
      console.log('💾 會話已保存到本地存儲:', session.id)
    } catch (error) {
      console.error('❌ 保存會話失敗:', error)
    }
  }, [])

  // 從本地存儲載入會話
  const loadSessionFromStorage = useCallback(async (sessionId: string): Promise<ChatSession | null> => {
    try {
      const session = await storageService.get<ChatSession>(`chat_session_${sessionId}`)
      if (session) {
        console.log('📖 從本地存儲載入會話:', sessionId)
        return session
      }
    } catch (error) {
      console.error('❌ 載入會話失敗:', error)
    }
    return null
  }, [])

  // 構建聊天請求
  const buildChatRequest = useCallback((
    question: string,
    sessionId: string,
    chatMode: 'knowledge_base' | 'general' | 'attachment' = 'general'
  ): OmnichannelChatRequest => {
    return {
      tenant_id: userInfo.tenant_id,
      question,
      source: ['*'],
      version: ['*'],
      tags: ['*'],
      service_id: userInfo.service_id,
      user_id: userInfo.user_id,
      session_id: sessionId,
      chat_mode: chatMode,
      channel: 'mobile',
    }
  }, [userInfo])

  // 發送聊天消息
  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!currentSession || !userInfo.tenant_id) {
      Toast.show('請先創建會話或設置用戶信息')
      return
    }

    setError(null)
    setChatLoading(true)

    try {
      // 創建並添加用戶消息
      const userMessage = createUserMessage(message)
      addMessageToCurrentSession(userMessage)

      // 構建請求
      const request = buildChatRequest(message, currentSession.id)

      console.log('💬 發送聊天請求:', {
        question: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
        session_id: currentSession.id,
      })

      // 調用 API
      const response = await apiService.post<ChatResponse>('omnichannel', '/chat', request)

      console.log('🔍 完整響應結構:', {
        response: response,
        responseCode: response.code,
        responseData: response.data,
        innerCode: response.data?.code,
        innerAnswer: response.data?.answer,
        answerType: typeof response.data?.answer
      })

      // 檢查外層響應是否成功 (HTTP 200)
      if (response.code === 200 && response.data) {
        // 檢查內層業務邏輯是否成功 (業務 code === 0)
        if (response.data.code === 0 && response.data.answer) {
          // 創建並添加助手消息
          const assistantMessage = createAssistantMessage(response.data.answer)
          console.log('📝 準備添加助手消息到會話:', assistantMessage)
          addMessageToCurrentSession(assistantMessage)
          console.log('✅ 助手消息已添加到會話')

          // 保存會話到本地存儲
          const updatedSession = {
            ...currentSession,
            messages: [...currentSession.messages, userMessage, assistantMessage],
            updated_at: new Date().toISOString(),
          }
          await saveSessionToStorage(updatedSession)

          console.log('✅ 聊天響應成功')
        } else {
          // 業務邏輯失敗
          throw new Error(response.data?.message || `業務邏輯失敗 (code: ${response.data?.code})`)
        }
      } else {
        // HTTP 請求失敗
        throw new Error(response.message || `HTTP 請求失敗 (code: ${response.code})`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '發送消息失敗'
      setError(errorMessage)
      Toast.show(errorMessage)
      console.error('❌ 聊天請求失敗:', error)
    } finally {
      setChatLoading(false)
    }
  }, [
    currentSession,
    userInfo,
    createUserMessage,
    createAssistantMessage,
    addMessageToCurrentSession,
    buildChatRequest,
    saveSessionToStorage,
    setChatLoading,
  ])

  // 創建新會話
  const createNewChatSession = useCallback((): ChatSession => {
    const newSession = createNewSession()
    console.log('🆕 創建新會話:', newSession.id)
    return newSession
  }, [createNewSession])

  // 清空當前會話
  const clearCurrentSession = useCallback(() => {
    if (currentSession) {
      const clearedSession: ChatSession = {
        ...currentSession,
        messages: [],
        updated_at: new Date().toISOString(),
      }
      setCurrentSession(clearedSession)
      console.log('🧹 清空會話:', currentSession.id)
    }
  }, [currentSession, setCurrentSession])

  return {
    // 狀態
    currentSession,
    chatLoading,
    error,

    // 方法
    sendMessage,
    createNewChatSession,
    clearCurrentSession,
    generateMessageId,
    generateSessionId,
    saveSessionToStorage,
    loadSessionFromStorage,

    // 工具方法
    createUserMessage,
    createAssistantMessage,
    buildChatRequest,
  }
}

export default useChatService
